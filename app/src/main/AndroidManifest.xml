<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM"/>
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="android.permission.USE_EXACT_ALARM"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK"/>
    <uses-permission android:name="com.android.alarm.permission.SET_ALARM"/>


    <application
            android:allowBackup="true"
            android:dataExtractionRules="@xml/data_extraction_rules"
            android:fullBackupContent="@xml/backup_rules"
            android:icon="@drawable/ic_alarm_icon_app"
            android:label="Alarm App"
            android:roundIcon="@drawable/ic_alarm_icon_app"
            android:supportsRtl="true"
            android:theme="@style/Theme.AppUseKotlin"
            tools:targetApi="31">
        <activity
                android:name=".view.AlarmActivity"
                android:exported="false"/>
        <activity
                android:name=".view.AddAlarmActivity"
                android:exported="false"/>
        <activity
                android:name=".view.MainActivity"
                android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <receiver
                android:name=".receiver.AlarmDismissReceiver"
                android:enabled="true"
                android:exported="false"/>

        <receiver
                android:name=".receiver.AlarmSnoozeReceiver"
                android:enabled="true"
                android:exported="false"/>

        <service
                android:name=".service.AlarmSoundService"
                android:enabled="true"
                android:exported="false"
                android:foregroundServiceType="mediaPlayback"/>
        <receiver
                android:name=".receiver.AlarmReceiver"
                android:enabled="true"
                android:exported="true"/>
    </application>

</manifest>
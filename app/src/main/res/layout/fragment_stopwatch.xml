<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="#FFF0F0">

    <TextView
        android:id="@+id/stopwatchTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Stopwatch"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="?attr/colorPrimary"
        android:padding="16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:id="@+id/stopwatchTimeText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="00:00:00"
        android:textSize="48sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:fontFamily="monospace"
        app:layout_constraintTop_toBottomOf="@id/stopwatchTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="8dp" />

    <LinearLayout
        android:id="@+id/buttonsLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@id/stopwatchTimeText"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp"
        android:layout_marginHorizontal="32dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/resetButton"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_weight="1"
            app:icon="@drawable/ic_refresh"
            app:iconGravity="textStart"
            app:iconPadding="0dp"
            app:iconSize="24dp"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
            app:cornerRadius="30dp"
            android:enabled="false"
            android:layout_marginEnd="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/startStopButton"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_weight="1.5"
            app:icon="@drawable/ic_play"
            app:iconGravity="textStart"
            app:iconPadding="0dp"
            app:iconSize="28dp"
            app:cornerRadius="30dp"
            android:layout_marginHorizontal="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/lapButton"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_weight="1"
            app:icon="@drawable/ic_flag"
            app:iconGravity="textStart"
            app:iconPadding="0dp"
            app:iconSize="24dp"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
            app:cornerRadius="30dp"
            android:enabled="false"
            android:layout_marginStart="8dp" />
    </LinearLayout>

    <!-- Progress Bars Section -->
    <LinearLayout
        android:id="@+id/progressBarsLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/buttonsLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="24dp"
        android:layout_marginHorizontal="16dp">

        <!-- Previous Lap -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:text="Previous"
                android:textSize="12sp"
                android:textColor="@android:color/holo_blue_bright"
                android:textStyle="bold" />

            <ProgressBar
                android:id="@+id/previousProgressBar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="0dp"
                android:layout_height="8dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="12dp"
                android:progressTint="@android:color/holo_blue_bright"
                android:progressBackgroundTint="#E0E0E0"
                android:max="100"
                android:progress="0" />

            <TextView
                android:id="@+id/previousTimeText"
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:text="0.00"
                android:textSize="12sp"
                android:textAlignment="textEnd"
                android:textColor="@android:color/black" />
        </LinearLayout>

        <!-- Fastest Lap -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:text="Fastest"
                android:textSize="12sp"
                android:textColor="@android:color/holo_red_light"
                android:textStyle="bold" />

            <ProgressBar
                android:id="@+id/fastestProgressBar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="0dp"
                android:layout_height="8dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="12dp"
                android:progressTint="@android:color/holo_red_light"
                android:progressBackgroundTint="#E0E0E0"
                android:max="100"
                android:progress="0" />

            <TextView
                android:id="@+id/fastestTimeText"
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:text="0.00"
                android:textSize="12sp"
                android:textAlignment="textEnd"
                android:textColor="@android:color/black" />
        </LinearLayout>

        <!-- Slowest Lap -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:text="Slowest"
                android:textSize="12sp"
                android:textColor="@android:color/holo_green_light"
                android:textStyle="bold" />

            <ProgressBar
                android:id="@+id/slowestProgressBar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="0dp"
                android:layout_height="8dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="12dp"
                android:progressTint="@android:color/holo_green_light"
                android:progressBackgroundTint="#E0E0E0"
                android:max="100"
                android:progress="0" />

            <TextView
                android:id="@+id/slowestTimeText"
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:text="0.00"
                android:textSize="12sp"
                android:textAlignment="textEnd"
                android:textColor="@android:color/black" />
        </LinearLayout>

        <!-- Average Lap -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:text="Average"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:textStyle="bold" />

            <ProgressBar
                android:id="@+id/averageProgressBar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="0dp"
                android:layout_height="8dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="12dp"
                android:progressTint="@android:color/darker_gray"
                android:progressBackgroundTint="#E0E0E0"
                android:max="100"
                android:progress="0" />

            <TextView
                android:id="@+id/averageTimeText"
                android:layout_width="60dp"
                android:layout_height="wrap_content"
                android:text="0.00"
                android:textSize="12sp"
                android:textAlignment="textEnd"
                android:textColor="@android:color/black" />
        </LinearLayout>
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/lapTimesRecyclerView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/progressBarsLayout"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="8dp"
        android:paddingHorizontal="16dp"
        android:paddingVertical="8dp"
        tools:listitem="@layout/item_lap_time"/>

</androidx.constraintlayout.widget.ConstraintLayout>
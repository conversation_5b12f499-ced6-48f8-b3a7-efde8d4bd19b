<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#FFF0F0"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/alarmTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Alarm"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="?attr/colorPrimary"
        android:padding="16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/noAlarmText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Không có báo thức nào"
        android:textSize="16sp"
        android:textColor="?attr/colorPrimary"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/alarmTitle"
        app:layout_constraintBottom_toTopOf="@id/addAlarmFab"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:gravity="center" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/alarmRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/alarmTitle"
        tools:listitem="@layout/item_alarm" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/addAlarmFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:srcCompat="@drawable/ic_add"
        android:contentDescription="Thêm báo thức"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_margin="16dp" />
</androidx.constraintlayout.widget.ConstraintLayout>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:layout_width="match_parent" android:layout_height="wrap_content" android:padding="16dp" android:layout_marginBottom="10dp" android:background="@drawable/lap_item_background">
    <TextView android:id="@+id/lapNumberText" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="16" android:textSize="24sp" android:textStyle="bold" android:textColor="@android:color/black" app:layout_constraintTop_toTopOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintBottom_toBottomOf="parent"/>
    <LinearLayout android:layout_width="0dp" android:layout_height="wrap_content" android:orientation="vertical" android:layout_marginStart="24dp" app:layout_constraintTop_toTopOf="parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toEndOf="@id/lapNumberText" app:layout_constraintEnd_toEndOf="parent">
        <TextView android:id="@+id/lapTimeText" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="9.15" android:textSize="20sp" android:textStyle="bold" android:textColor="@android:color/black" android:fontFamily="monospace"/>
        <TextView android:id="@+id/elapsedTimeText" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Elapsed Time: 29:51.75" android:textSize="14sp" android:textColor="@android:color/darker_gray" android:layout_marginTop="2dp"/>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
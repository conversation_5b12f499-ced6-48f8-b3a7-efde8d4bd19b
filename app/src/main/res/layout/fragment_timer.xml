<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:layout_width="match_parent" android:layout_height="match_parent" android:background="#FFF0F0">
    <TextView android:id="@+id/titleTimerFragment" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Timer" android:textSize="24sp" android:textStyle="bold" android:textColor="?attr/colorPrimary" android:padding="16dp" app:layout_constraintTop_toTopOf="parent" app:layout_constraintStart_toStartOf="parent"/>
    <!--  <PERSON><PERSON><PERSON> di<PERSON>n chọn thời gian  -->
    <LinearLayout android:id="@+id/timeSelectionLayout" android:layout_width="match_parent" android:layout_height="match_parent" android:orientation="vertical" android:gravity="center" android:visibility="visible">
        <TextView android:id="@+id/timerTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Thời gian đếm ngược" android:textSize="24sp" android:textStyle="bold" android:textColor="?attr/colorPrimary" android:padding="16dp" android:layout_marginBottom="32dp"/>
        <LinearLayout android:id="@+id/timePickersLayout" android:layout_width="wrap_content" android:layout_height="wrap_content" android:orientation="horizontal" android:gravity="center" android:layout_marginBottom="48dp">
            <!--  Giờ  -->
            <LinearLayout android:layout_width="wrap_content" android:layout_height="wrap_content" android:orientation="vertical" android:gravity="center" android:layout_marginEnd="16dp">
                <TextView android:id="@+id/hourLabel" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Giờ" android:textSize="16sp" android:layout_marginBottom="8dp"/>
                <NumberPicker android:id="@+id/hourPicker" android:layout_width="80dp" android:layout_height="120dp"/>
            </LinearLayout>
            <!--  Phút  -->
            <LinearLayout android:layout_width="wrap_content" android:layout_height="wrap_content" android:orientation="vertical" android:gravity="center" android:layout_marginHorizontal="16dp">
                <TextView android:id="@+id/minuteLabel" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Phút" android:textSize="16sp" android:layout_marginBottom="8dp"/>
                <NumberPicker android:id="@+id/minutePicker" android:layout_width="80dp" android:layout_height="120dp"/>
            </LinearLayout>
            <!--  Giây  -->
            <LinearLayout android:layout_width="wrap_content" android:layout_height="wrap_content" android:orientation="vertical" android:gravity="center" android:layout_marginStart="16dp">
                <TextView android:id="@+id/secondLabel" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Giây" android:textSize="16sp" android:layout_marginBottom="8dp"/>
                <NumberPicker android:id="@+id/secondPicker" android:layout_width="80dp" android:layout_height="120dp"/>
            </LinearLayout>
        </LinearLayout>
        <!--  Nút Start  -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton android:id="@+id/startButton" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/ic_play" android:backgroundTint="@android:color/white" app:tint="?attr/colorPrimary" app:elevation="8dp" app:fabSize="normal" android:contentDescription="Bắt đầu timer"/>
    </LinearLayout>
    <!--  Giao diện đếm ngược với vòng tròn  -->
    <LinearLayout android:id="@+id/countdownLayout" android:layout_width="match_parent" android:layout_height="match_parent" android:orientation="vertical" android:gravity="center" android:visibility="gone">
        <!--  Vòng tròn đếm ngược  -->
        <RelativeLayout android:layout_width="300dp" android:layout_height="300dp" android:layout_marginBottom="48dp">
            <!--  Progress Circle  -->
            <ProgressBar android:id="@+id/circularProgressBar" android:layout_width="match_parent" android:layout_height="match_parent" style="?android:attr/progressBarStyleHorizontal" android:indeterminate="false" android:max="100" android:progress="100" android:progressDrawable="@drawable/circular_progress_drawable" android:rotation="-90"/>
            <!--  Thời gian hiển thị trong vòng tròn  -->
            <TextView android:id="@+id/timerDisplay" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_centerInParent="true" android:text="00:00:00" android:textSize="32sp" android:textStyle="bold" android:textColor="@android:color/black" android:fontFamily="monospace"/>
        </RelativeLayout>
        <!--  Nút điều khiển  -->
        <LinearLayout android:layout_width="wrap_content" android:layout_height="wrap_content" android:orientation="horizontal" android:gravity="center">
            <com.google.android.material.floatingactionbutton.FloatingActionButton android:id="@+id/stopButton" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/ic_stop" android:backgroundTint="@android:color/white" app:tint="@android:color/holo_red_dark" app:elevation="8dp" app:fabSize="normal" android:layout_marginEnd="32dp" android:contentDescription="Dừng timer"/>
            <com.google.android.material.floatingactionbutton.FloatingActionButton android:id="@+id/pauseResumeButton" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/ic_pause" android:backgroundTint="@android:color/white" app:tint="@android:color/holo_orange_dark" app:elevation="8dp" app:fabSize="normal" android:contentDescription="Tạm dừng timer"/>
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    android:layout_marginTop="4dp"
    android:layout_marginEnd="8dp"
    android:layout_marginBottom="4dp"
    android:background="@color/back"
    app:cardCornerRadius="28dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- <PERSON><PERSON><PERSON> hi<PERSON><PERSON> th<PERSON> b<PERSON><PERSON>ứ<PERSON> (l<PERSON><PERSON><PERSON> hiện) -->
        <LinearLayout
            android:id="@+id/alarmDisplayLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#FBFBF4"
            android:clickable="true"
            android:focusable="true"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:paddingLeft="10dp">

                    <TextView
                        android:id="@+id/alarmTimeTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="08:30 AM"
                        android:textColor="@color/black"
                        android:textSize="28sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/alarmDaysTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="Mon, Tue, Wed, Thu, Fri"
                        android:textColor="@android:color/darker_gray"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/alarmLabelTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:text="Đi làm"
                        android:textColor="@android:color/darker_gray"
                        android:textSize="14sp"
                        android:textStyle="italic"
                        android:visibility="gone" />
                </LinearLayout>

                <Switch
                    android:id="@+id/alarmToggleSwitch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="4dp"
                    android:checked="true"
                    android:minWidth="48dp"
                    android:minHeight="48dp"
                    android:thumbTint="#663399"
                    android:trackTint="#B19CD9"
                    tools:ignore="UseSwitchCompatOrMaterialXml" />

                <ImageButton
                    android:id="@+id/deleteAlarmButton"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginStart="4dp"
                    android:background="@android:color/transparent"
                    android:contentDescription="Xóa báo thức"
                    android:paddingRight="4dp"
                    android:src="@android:drawable/ic_menu_delete"
                    app:tint="#663399"
                    tools:ignore="TouchTargetSizeCheck" />
            </LinearLayout>
        </LinearLayout>

        <!-- Phần chỉnh sửa (ẩn/hiện) -->
        <LinearLayout
            android:id="@+id/alarmEditLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#F8F9FA"
            android:orientation="vertical"
            android:padding="16dp"
            android:visibility="gone">

            <!-- Time Picker -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="Thời gian:"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TimePicker
                android:id="@+id/editTimePicker"
                android:layout_width="wrap_content"
                android:layout_height="120dp"
                android:layout_gravity="center"
                android:layout_marginBottom="16dp"
                android:timePickerMode="spinner" />

            <!-- Label Input -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="Tên báo thức:"
                android:textSize="16sp"
                android:textStyle="bold" />

            <com.google.android.material.textfield.TextInputLayout
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Nhập tên báo thức">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editAlarmLabelEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textCapSentences" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- Days of Week -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="Ngày trong tuần:"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:gravity="center"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/editCheckSun"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textSize="10dp"
                    android:text="CN" />

                <CheckBox
                    android:id="@+id/editCheckMon"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textSize="10dp"
                    android:text="T2" />

                <CheckBox
                    android:id="@+id/editCheckTue"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textSize="10dp"
                    android:text="T3" />

                <CheckBox
                    android:id="@+id/editCheckWed"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textSize="10dp"
                    android:text="T4" />

                <CheckBox
                    android:id="@+id/editCheckThu"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textSize="10dp"
                    android:text="T5" />

                <CheckBox
                    android:id="@+id/editCheckFri"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textSize="10dp"
                    android:text="T6" />

                <CheckBox
                    android:id="@+id/editCheckSat"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textSize="10dp"
                    android:text="T7" />
            </LinearLayout>

            <!-- Ringtone Selection -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="Âm thanh báo thức:"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/editSelectRingtoneButton"
                    style="?attr/materialButtonOutlinedStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Chọn nhạc chuông" />

                <TextView
                    android:id="@+id/editRingtoneNameTextView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1"
                    android:text="Nhạc mặc định"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/cancelEditButton"
                    style="?attr/materialButtonOutlinedStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="Hủy" />

                <Button
                    android:id="@+id/saveEditButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Lưu" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>